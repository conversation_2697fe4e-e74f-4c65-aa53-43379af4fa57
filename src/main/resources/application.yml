spring:
  application:
    name: deepseek-doctor   # 定义当前项目的应用名称
  profiles:
    active: prod
  # Redis 配置
  data:
    redis:
      host: *************
      port: 6379
      timeout: 2000ms
      database: 0
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  # AI 向量数据库配置
  ai:
    vectorstore:
      redis:
        uri: redis://*************:6379
        index: vector-index
        prefix: "doc:"
    ollama:
      base-url: http://127.0.0.1:11434
      embedding:
        model: nomic-embed-text
#      chat:
#        model: my-doctor:1.0.1.Release

# MyBatisPlus 的配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      update-strategy: not_empty
  mapper-locations: classpath*:/mappers/*.xml