spring:
  application:
    name: deepseek-doctor-test
  profiles:
    active: test
  
  # 测试环境 Redis 配置
  data:
    redis:
      host: *************
      port: 6379
      timeout: 5000ms
      database: 1  # 使用不同的数据库避免与生产环境冲突
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # AI 向量数据库测试配置
  ai:
    vectorstore:
      redis:
        uri: redis://*************:6379/1
        index: test-vector-index
        prefix: "test:doc:"
        # 测试环境可以使用较小的向量维度
        dimension: 1536
    ollama:
      base-url: http://127.0.0.1:11434
      embedding:
        model: nomic-embed-text

# 日志配置
logging:
  level:
    com.itzixi: DEBUG
    org.springframework.ai: DEBUG
    org.springframework.data.redis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# MyBatisPlus 测试配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      update-strategy: not_empty
